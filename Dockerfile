# ===== STAGE 1: Dependencies =====
FROM node:20-alpine AS deps

# Cài đặt các dependencies cần thiết
RUN apk add --no-cache libc6-compat python3 make g++ git

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt yarn globally
RUN corepack enable && corepack prepare yarn@stable --activate

# Sao chép package files
COPY package.json yarn.lock* ./

# Cài đặt dependencies
RUN yarn install --frozen-lockfile

# ===== STAGE 2: Builder =====
FROM node:20-alpine AS builder

WORKDIR /app

# Cài đặt yarn
RUN corepack enable && corepack prepare yarn@stable --activate

# Sao chép node_modules từ deps stage
COPY --from=deps /app/node_modules ./node_modules

# Sao chép source code
COPY . .

# Build ứng dụng
RUN yarn build

# ===== STAGE 3: Production Dependencies =====
FROM node:20-alpine AS prod-deps

WORKDIR /app

# Cài đặt yarn
RUN corepack enable && corepack prepare yarn@stable --activate

# Sao chép package files
COPY package.json yarn.lock* ./

# Cài đặt chỉ production dependencies
RUN yarn install --frozen-lockfile --production

# ===== STAGE 4: Production Runtime =====
FROM node:20-alpine AS production

# Cài đặt các dependencies runtime cần thiết
RUN apk add --no-cache \
    dumb-init \
    tini \
    && rm -rf /var/cache/apk/*

# Tạo user non-root để chạy ứng dụng an toàn hơn
RUN addgroup -g 1001 -S nodejs && \
    adduser -S appuser -u 1001 -G nodejs

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt yarn
RUN corepack enable && corepack prepare yarn@stable --activate

# Sao chép package files
COPY --from=builder /app/package.json /app/yarn.lock ./

# Sao chép production node_modules
COPY --from=prod-deps /app/node_modules ./node_modules

# Sao chép built application
COPY --from=builder /app/dist ./dist

# Sao chép copy-env.js script
COPY --from=builder /app/copy-env.js ./

# Sao chép .mastra nếu tồn tại (optional)
COPY --from=builder /app/.mastra ./.mastra 2>/dev/null || true

# Sao chép .env.example nếu tồn tại (optional)
COPY --from=builder /app/.env.example ./.env.example 2>/dev/null || true

# Thay đổi ownership cho user appuser
RUN chown -R appuser:nodejs /app

# Chuyển sang user non-root
USER appuser

# Thiết lập biến môi trường cho production
ENV NODE_ENV=production \
    PORT=3000 \
    NODE_OPTIONS="--max-old-space-size=1024"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Expose port
EXPOSE 3000

# Sử dụng tini để xử lý tín hiệu một cách chính xác
ENTRYPOINT ["tini", "--"]

# Khởi động ứng dụng
CMD ["yarn", "start"]
